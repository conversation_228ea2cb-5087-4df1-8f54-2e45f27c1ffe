"use client";

import { useRouter, useSearchParams } from "next/navigation";
import { useEffect } from "react";

import { useHubspotCallback } from "@pearl/api-hooks";

import { useCRM } from "~/features/crm/context/CRMContext";
import { CRMCallbackContent } from "~/features/onboarding/components/CRMCallbackContent";

export default function HubspotCallbackPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { refreshUserCRM } = useCRM();

  const code = searchParams.get("code");
  const state = searchParams.get("state");

  const {
    isLoading,
    isImporting,
    isSuccess,
    isError,
    errorMessage,
    processCallback,
  } = useHubspotCallback();

  useEffect(() => {
    if (code && state) {
      void processCallback(code, state);
    }
  }, [code, state, processCallback]);

  useEffect(() => {
    if (isSuccess) {
      const refresh = async () => {
        await refreshUserCRM();
        router.push("/");
      };

      const timer = setTimeout(() => {
        void refresh();
      }, 5000);

      return () => clearTimeout(timer);
    }

    if (isError) {
      const timer = setTimeout(() => {
        router.push("/onboarding");
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [isSuccess, isError, router, refreshUserCRM]);

  return (
    <CRMCallbackContent
      crmId="hubspot"
      isLoading={isLoading}
      isImporting={isImporting}
      isSuccess={isSuccess}
      isError={isError}
      errorMessage={errorMessage}
    />
  );
}
