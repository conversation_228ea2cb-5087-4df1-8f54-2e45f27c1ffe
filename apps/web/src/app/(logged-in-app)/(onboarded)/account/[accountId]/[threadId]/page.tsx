"use client";

import { useParams } from "next/navigation";

import { SidebarTrigger } from "~/components/ui/Sidebar";
import ChatContainer from "~/features/chat/components/ChatContainer";

export default function Thread() {
  const { accountId, threadId } = useParams();

  return (
    <>
      <SidebarTrigger className="md:hidden" />
      <ChatContainer
        threadId={threadId as string}
        accountId={accountId as string}
      />
    </>
  );
}
