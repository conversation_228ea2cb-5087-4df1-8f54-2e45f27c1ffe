"use client";

import { usePara<PERSON>, useRouter } from "next/navigation";

import { SidebarTrigger } from "~/components/ui/Sidebar";
import ChatContainer from "~/features/chat/components/ChatContainer";

export default function Account() {
  const { accountId } = useParams<{ accountId: string }>();

  const router = useRouter();

  const handleNewThread = (threadId: string) => {
    router.push(`/account/${accountId}/${threadId}`);
  };

  return (
    <>
      <SidebarTrigger className="md:hidden" />
      <ChatContainer
        threadId={undefined}
        accountId={accountId}
        onNewThread={handleNewThread}
      />
    </>
  );
}
