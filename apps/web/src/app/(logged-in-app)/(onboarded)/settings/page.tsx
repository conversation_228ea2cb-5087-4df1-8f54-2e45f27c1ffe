"use client";

import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { Suspense, useEffect, useState } from "react";

import { IntegrationsList } from "~/components/IntegrationsList";
import PageHeader from "~/components/PageHeader";

const navigationItems = [
  { id: "integrations", label: "Integrations", href: "/settings" },
];

function SettingsContent() {
  const searchParams = useSearchParams();
  const [activeTab, setActiveTab] = useState("integrations");

  useEffect(() => {
    const tab = searchParams.get("tab") || "integrations";
    setActiveTab(tab);
  }, [searchParams]);

  const renderContent = () => {
    switch (activeTab) {
      case "integrations":
        return <IntegrationsList />;
    }
  };

  return (
    <div className="">
      <div className="mt-8 px-4 sm:px-6 lg:px-8">
        <PageHeader variant="minimal" title="Settings" />
      </div>

      <div className="mt-10">
        <div className="px-4 sm:px-6 lg:px-8">
          <div className="border-b border-border">
            <div className="scrollbar-hide flex w-full overflow-x-auto">
              {navigationItems.map((item, index) => (
                <Link
                  key={item.id}
                  href={`${item.href}?tab=${item.id}`}
                  className={`relative flex-shrink-0 py-3 text-sm font-medium transition-colors ${index === 0 ? "mr-4 ml-0" : "mx-4"} ${
                    activeTab === item.id
                      ? "text-foreground"
                      : "text-muted-foreground hover:text-foreground"
                  } `}
                >
                  {item.label}
                  {activeTab === item.id && (
                    <div className="absolute right-0 bottom-0 left-0 h-0.5 bg-primary"></div>
                  )}
                </Link>
              ))}
            </div>
          </div>
        </div>

        <div className="mt-10 px-4 pb-12 sm:px-6 lg:px-8">
          {renderContent()}
        </div>
      </div>
    </div>
  );
}

// Loading fallback component
function SettingsLoading() {
  return (
    <div>
      <div className="mt-8 px-4 sm:px-6 lg:px-8">
        <PageHeader variant="minimal" title="Settings" />
      </div>

      <div className="mt-10">
        <div className="px-4 sm:px-6 lg:px-8">
          <div className="border-b border-border">
            <div className="scrollbar-hide flex w-full overflow-x-auto">
              {navigationItems.map((item, index) => (
                <div
                  key={item.id}
                  className={`flex-shrink-0 py-3 text-sm font-medium text-muted-foreground ${index === 0 ? "mr-4 ml-0" : "mx-4"} `}
                >
                  {item.label}
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="mt-10 px-4 pb-12 sm:px-6 lg:px-8">
          <div className="animate-pulse">
            <div className="mb-4 h-8 w-1/4 rounded bg-muted"></div>
            <div className="mb-2 h-4 w-3/4 rounded bg-muted"></div>
            <div className="h-4 w-1/2 rounded bg-muted"></div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function SettingsPage() {
  return (
    <Suspense fallback={<SettingsLoading />}>
      <SettingsContent />
    </Suspense>
  );
}
