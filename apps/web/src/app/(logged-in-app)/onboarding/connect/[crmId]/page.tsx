"use client";

import { notFound } from "next/navigation";
import { use } from "react";

import { CRMConnectionStep } from "~/features/onboarding/components/CRMConnectionStep";

interface ConnectCRMPageProps {
  params: Promise<{
    crmId: string;
  }>;
}

const SUPPORTED_CRMS = ["salesforce", "hubspot"];

export default function ConnectCRMPage({ params }: ConnectCRMPageProps) {
  const { crmId } = use(params);

  if (!SUPPORTED_CRMS.includes(crmId)) {
    notFound();
  }

  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-6">
      <CRMConnectionStep crmId={crmId} />
    </div>
  );
}
