"use client";

import { notFound, useRouter, useSearchParams } from "next/navigation";
import { use, useEffect } from "react";

import { useHubspotCallback, useSalesforceCallback } from "@pearl/api-hooks";

import { useCRM } from "~/features/crm/context/CRMContext";
import { CRMCallbackContent } from "~/features/onboarding/components/CRMCallbackContent";

interface CRMCallbackPageProps {
  params: Promise<{
    crmId: string;
  }>;
}

const SUPPORTED_CRMS = ["salesforce", "hubspot"];

export default function CRMCallbackPage({ params }: CRMCallbackPageProps) {
  const { crmId } = use(params);
  const router = useRouter();
  const searchParams = useSearchParams();
  const { refreshUserCRM } = useCRM();

  if (!SUPPORTED_CRMS.includes(crmId)) {
    notFound();
  }

  const code = searchParams.get("code");
  const state = searchParams.get("state");

  // Use the appropriate callback hook based on CRM type
  const salesforceCallback = useSalesforceCallback();
  const hubspotCallback = useHubspotCallback();

  const callback =
    crmId === "salesforce" ? salesforceCallback : hubspotCallback;
  const {
    isLoading,
    isImporting,
    isSuccess,
    isError,
    errorMessage,
    processCallback,
  } = callback;

  useEffect(() => {
    if (code && state) {
      void processCallback(code, state);
    }
  }, [code, state, processCallback]);

  useEffect(() => {
    if (isSuccess) {
      const refresh = async () => {
        await refreshUserCRM();
        router.push("/");
      };

      const timer = setTimeout(() => {
        void refresh();
      }, 5000);

      return () => clearTimeout(timer);
    }

    if (isError) {
      const timer = setTimeout(() => {
        router.push("/onboarding");
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [isSuccess, isError, router, refreshUserCRM]);

  return (
    <CRMCallbackContent
      crmId={crmId}
      isLoading={isLoading}
      isImporting={isImporting}
      isSuccess={isSuccess}
      isError={isError}
      errorMessage={errorMessage}
    />
  );
}
