import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { useApiClient } from "@pearl/api-client";
import type { Integration } from "@pearl/api-hooks";
import { useDeleteIntegration } from "@pearl/api-hooks";

import { getStrategy } from "~/features/integrations/registry";

function getIntegrationDisplayName(
  integration: Integration,
  strategy: ReturnType<typeof getStrategy>,
): string {
  return strategy.displayName ?? integration.name;
}

async function handleConnectionSuccess(
  integration: Integration,
  strategy: ReturnType<typeof getStrategy>,
  queryClient: ReturnType<typeof useQueryClient>,
): Promise<void> {
  const displayName = getIntegrationDisplayName(integration, strategy);
  toast.success(`${displayName} connected successfully!`);
  await queryClient.invalidateQueries({ queryKey: ["integrations"] });
}

export async function handleRedirectOAuthSuccess(
  integrationId: string,
  queryClient: ReturnType<typeof useQueryClient>,
): Promise<void> {
  const strategy = getStrategy(integrationId);
  const integration: Integration = {
    id: integrationId,
    name: strategy.displayName ?? integrationId,
    description: "",
    integrationType: "",
    source: "",
    isActive: false,
  };

  await handleConnectionSuccess(integration, strategy, queryClient);
}

export function useIntegrationActions(integration: Integration) {
  const { apiClient } = useApiClient();
  const strategy = getStrategy(integration.id);
  const extraHook = strategy.useExtraData?.() ?? {
    data: undefined,
    isLoading: false,
  };

  const queryClient = useQueryClient();

  const { mutate: connect, isPending: isConnecting } = useMutation({
    mutationFn: () =>
      strategy.connect({
        apiClient,
        extra: extraHook.data,
        integration,
      }),
    onSuccess: async () => {
      if (strategy.oauthFlowType !== "redirect") {
        await handleConnectionSuccess(integration, strategy, queryClient);
      }
    },
    onError: () => {
      const displayName = getIntegrationDisplayName(integration, strategy);
      toast.error(`Failed to connect ${displayName}. Please try again.`);
    },
  });

  const { mutate: genericDisconnect, isPending: isDisconnecting } =
    useDeleteIntegration();

  const disconnect = () => {
    const displayName = getIntegrationDisplayName(integration, strategy);

    if (strategy.disconnect) {
      return strategy
        .disconnect({ apiClient, integration })
        .then(async () => {
          toast.success(`${displayName} disconnected successfully!`);
          await queryClient.invalidateQueries({ queryKey: ["integrations"] });
        })
        .catch(() =>
          toast.error(`Failed to disconnect ${displayName}. Please try again.`),
        );
    }

    genericDisconnect(integration.id, {
      onSuccess: () => {
        toast.success(`${displayName} disconnected successfully!`);
        void queryClient.invalidateQueries({ queryKey: ["integrations"] });
      },
      onError: () =>
        toast.error(`Failed to disconnect ${displayName}. Please try again.`),
    });
  };

  return {
    connect,
    disconnect,
    isConnecting,
    isDisconnecting,
    extraLoading: extraHook.isLoading,
  };
}
