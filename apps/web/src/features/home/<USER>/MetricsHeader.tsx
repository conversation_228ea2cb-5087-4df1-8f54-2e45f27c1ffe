import { cn } from "~/lib/tailwind/cn";

interface MetricsHeaderProps {
  firstName?: string;
  className?: string;
}

export function MetricsHeader({ firstName, className }: MetricsHeaderProps) {
  return (
    <div
      className={cn(
        "mb-10 flex w-full max-w-2xl flex-col gap-4 px-4",
        className,
      )}
    >
      <div className="flex items-center justify-center sm:justify-start">
        <h2 className="text-2xl leading-8 font-semibold text-foreground">
          Hey {firstName}, let's close
        </h2>
      </div>
      <div className="flex w-full flex-col items-start gap-4 px-4 sm:flex-row sm:items-center sm:gap-8 sm:px-0">
        <div className="flex w-full flex-col border-b border-gray-300 pb-4 sm:flex-1 sm:border-r sm:border-b-0 sm:pr-8 sm:pb-0 dark:border-gray-700">
          <span className="text-sm text-muted-foreground">Quota</span>
          <div className="mt-2 flex items-center gap-2">
            <span className="text-sm font-semibold text-foreground">
              $1,111,111
            </span>
          </div>
        </div>
        <div className="flex w-full flex-col border-b border-gray-300 pb-4 sm:flex-1 sm:border-r sm:border-b-0 sm:pr-8 sm:pb-0 dark:border-gray-700">
          <span className="text-sm text-muted-foreground">Closed won</span>
          <div className="mt-2 flex items-center gap-2">
            <span className="text-sm font-semibold text-foreground">
              $111,111
            </span>
          </div>
        </div>
        <div className="flex w-full flex-col border-b border-gray-300 pb-4 sm:flex-1 sm:border-r sm:border-b-0 sm:pr-8 sm:pb-0 dark:border-gray-700">
          <span className="text-sm text-muted-foreground">Pipeline</span>
          <div className="mt-2 flex items-center gap-2">
            <span className="text-sm font-semibold text-foreground">
              $111,111
            </span>
          </div>
        </div>
        <div className="flex w-full flex-col sm:flex-1">
          <span className="text-sm text-muted-foreground">Forecast</span>
          <div className="mt-2 flex items-center gap-2">
            <span className="text-sm font-semibold text-foreground">
              $111,111
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
